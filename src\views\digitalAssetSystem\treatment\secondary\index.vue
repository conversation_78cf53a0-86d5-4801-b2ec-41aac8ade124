<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="handlePlanReview">审核计划</el-button>
      </div>
      <el-button type="primary" icon="el-icon-search">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zccgId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <!-- 二级单位相关字段 -->
        <el-table-column prop="zccgSecondLevelUnit" label="二级成员单位" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgInvestmentEntity" label="投资主体" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgHoldingEnterpriseName" label="参股企业名称" width="160" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgFirstTimeForGroup" label="我方是否为第一大股东（集团合计）" width="200" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgFirstTimeForGroup || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="zccgHasActualController" label="是否有实际控制人" width="140" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgHasActualController || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="zccgDividendOverYears" label="是否满5年未分红" width="140" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgDividendOverYears || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="zccgIsLongTermLoss" label="是否长期亏损" width="120" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgIsLongTermLoss || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="zccgIsLowEfficiencyAsset" label="是否属于低效无效资产" width="160" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgIsLowEfficiencyAsset || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="zccgIsNonContinuingOperation" label="是否非持续经营" width="140" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgIsNonContinuingOperation || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="zccgTimelyDisclosureMajorMatters" label="是否能及时掌握财务数据和经营情况" width="220" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgTimelyDisclosureMajorMatters || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="zccgLiquidationYear" label="清退年份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgStatus" label="状态" width="100" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.zccgStatus)">
              {{ scope.row.zccgStatus || '未设置' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="zccgPlanningStatus" label="分析建议" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgSecondLevelUnitSuggestion" label="整改建议" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgIsProtected" label="是否保留" width="100" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgIsProtected || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="zccgIsCompliantExit" label="是否完成清退" width="120" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgIsCompliantExit || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            <el-button type="text" size="small" @click="handleProgressReporting(scope.row)">进展填报</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 进展填报弹窗 -->
    <ProgressReporting :visible.sync="progressReportingVisible" :initial-data="progressReportingData" @confirm="handleProgressReportingConfirm" />
  </div>
</template>

<script>
import { getSecondaryUnitList } from '@/api/digitalAssetSystem/treatment/secondary'
import ProgressReporting from './components/progressReporting.vue'

export default {
  components: {
    ProgressReporting
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 进展填报弹窗相关
      progressReportingVisible: false,
      progressReportingData: {},
      currentRowData: null
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getSecondaryUnitList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    handleDetail (row) {
      console.log('查看详情:', row)
    },



    // 进展填报
    handleProgressReporting (row) {
      this.currentRowData = row
      this.progressReportingData = {}
      this.progressReportingVisible = true
    },

    // 进展填报确认
    handleProgressReportingConfirm (formData) {
      console.log('进展填报提交:', formData)
      this.$message.success('进展填报提交成功')
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },

    // 获取状态标签类型
    getStatusType (status) {
      const statusMap = {
        '绿灯': 'success',
        '黄灯': 'warning',
        '红灯': 'danger'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
</style>
