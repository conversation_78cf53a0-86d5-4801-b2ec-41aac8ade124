<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="resetQuery">法人压减分析</el-button>
        <el-button type="primary">推送成员单位</el-button>
        <el-button type="primary" @click="handlePlanReview">计划审核</el-button>
      </div>
      <el-button type="primary">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zccrEnterpriseName">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zccrSecondaryMemberUnit" label="二级成员单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrEnterpriseName" label="企业名称" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrLegalPersonLevel" label="法人层级" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrManagementLevel" label="管理层级" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrIsLossInefficientEnterprise" label="是否亏损低效企业" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrIsEmptyShellEnterprise" label="是否为空壳企业" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrIsInsolventEnterprise" label="是否为资不抵债企业" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrIsThreeYearLossEnterprise" label="是否为连续三年亏损企业" width="160" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrIsMajorShareholderLessHalfBoard" label="是否为第一大股东但董事会席位低于一半" width="220" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrIsNetAssetLessRegisteredCapital" label="是否为净资产小于注册资本企业" width="200" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrWithdrawalYear" label="清退年份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrStatus" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <div class="status-block-container">
              <div class="status-block" :class="{
                  'status-danger': scope.row.zccrStatus === '清退',
                  'status-success': scope.row.zccrStatus === '保留',
                  'status-warning': scope.row.zccrStatus === '关注',
                  'status-info': !scope.row.zccrStatus || (scope.row.zccrStatus !== '清退' && scope.row.zccrStatus !== '保留' && scope.row.zccrStatus !== '关注')
                }">
                {{ scope.row.zccrStatus || '待定' }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="zccrAnalysisSuggestion" label="分析建议" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrRectificationSuggestion" label="整改建议" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrRectificationItems" label="整改事项" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrIsRetained" label="是否保留" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccrIsReductionCompleted" label="是否完成压减" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" min-width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleSuggestion(scope.row)">建议</el-button>
            <el-button type="text" size="small" @click="handleCorrection(scope.row)">修正</el-button>
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 计划审核弹窗 -->
    <PlanReviewDialog :visible.sync="planReviewDialogVisible" :initial-data="planReviewData" @confirm="handlePlanReviewConfirm" @view-plan="handleViewPlan" @select-plan="handleSelectPlan" @suggestion-confirm="handlePlanSuggestionConfirm" @correction-confirm="handlePlanCorrectionConfirm" @detail="handlePlanDetail" />

    <!-- 建议弹窗 -->
    <SuggestionDialog :visible.sync="suggestionDialogVisible" :initial-data="suggestionData" @confirm="handleSuggestionConfirm" />

    <!-- 修正弹窗 -->
    <CorrectDialog :visible.sync="correctionDialogVisible" :initial-data="correctionData" @confirm="handleCorrectionConfirm" />
  </div>
</template>

<script>
import { getCorporationReductionList } from '@/api/digitalAssetSystem/legalperson/corporationReduction.js'
import PlanReviewDialog from './components/planReviewDialog.vue'
import SuggestionDialog from './components/suggestionDialog.vue'
import CorrectDialog from './components/correctDialog.vue'

export default {
  components: {
    PlanReviewDialog,
    SuggestionDialog,
    CorrectDialog
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 计划审核弹窗相关
      planReviewDialogVisible: false,
      planReviewData: {},
      // 建议弹窗相关
      suggestionDialogVisible: false,
      suggestionData: {},
      // 修正弹窗相关
      correctionDialogVisible: false,
      correctionData: {}
    }
  },

  created () {
  },

  methods: {
    resetQuery () {
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.fetchData()
    },

    fetchData () {
      this.loading = true
      getCorporationReductionList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          // 如果接口暂时没有数据，使用模拟数据展示表格效果
          this.tableData = this.getMockData()
          this.total = this.tableData.length
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        // 接口调用失败时使用模拟数据
        this.tableData = this.getMockData()
        this.total = this.tableData.length
        console.warn('接口调用失败，使用模拟数据')
      })
    },

    // 模拟数据方法
    getMockData () {
      return [
        {
          zccrSerialNumber: '1',
          zccrSecondaryMemberUnit: 'xxx',
          zccrEnterpriseName: 'xxx',
          zccrLegalPersonLevel: '3',
          zccrManagementLevel: '3',
          zccrIsEmptyShellEnterprise: '是',
          zccrIsLossInefficientEnterprise: '是',
          zccrIsThreeYearLossEnterprise: '是',
          zccrIsInsolventEnterprise: '是',
          zccrIsRelatedToMainBusiness: '是',
          zccrIsMajorShareholderLessHalfBoard: '是',
          zccrIsNetAssetLessRegisteredCapital: '是',
          zccrWithdrawalYear: '2025',
          zccrStatus: '清退',
          zccrAnalysisSuggestion: '是',
          zccrRectificationItems: '资产处置',
          zccrRectificationSuggestion: '是',
          zccrIsRetained: '是',
          zccrIsReductionCompleted: '是'
        },
        {
          zccrSerialNumber: '2',
          zccrSecondaryMemberUnit: '',
          zccrEnterpriseName: '',
          zccrLegalPersonLevel: '',
          zccrManagementLevel: '',
          zccrIsEmptyShellEnterprise: '否',
          zccrIsLossInefficientEnterprise: '否',
          zccrIsThreeYearLossEnterprise: '否',
          zccrIsInsolventEnterprise: '否',
          zccrIsRelatedToMainBusiness: '否',
          zccrIsMajorShareholderLessHalfBoard: '否',
          zccrIsNetAssetLessRegisteredCapital: '否',
          zccrWithdrawalYear: '2025',
          zccrStatus: '关注',
          zccrAnalysisSuggestion: '整改',
          zccrRectificationItems: '经营改善',
          zccrRectificationSuggestion: 'xxx',
          zccrIsRetained: '否',
          zccrIsReductionCompleted: '否'
        },
        {
          zccrSerialNumber: '3',
          zccrSecondaryMemberUnit: '',
          zccrEnterpriseName: '',
          zccrLegalPersonLevel: '',
          zccrManagementLevel: '',
          zccrIsEmptyShellEnterprise: '否',
          zccrIsLossInefficientEnterprise: '否',
          zccrIsThreeYearLossEnterprise: '否',
          zccrIsInsolventEnterprise: '否',
          zccrIsRelatedToMainBusiness: '否',
          zccrIsMajorShareholderLessHalfBoard: '否',
          zccrIsNetAssetLessRegisteredCapital: '否',
          zccrWithdrawalYear: '',
          zccrStatus: '保留',
          zccrAnalysisSuggestion: '正常',
          zccrRectificationItems: '',
          zccrRectificationSuggestion: '',
          zccrIsRetained: '是',
          zccrIsReductionCompleted: '否'
        },
        {
          zccrSerialNumber: '4',
          zccrSecondaryMemberUnit: '',
          zccrEnterpriseName: '',
          zccrLegalPersonLevel: '',
          zccrManagementLevel: '',
          zccrIsEmptyShellEnterprise: '否',
          zccrIsLossInefficientEnterprise: '是',
          zccrIsThreeYearLossEnterprise: '否',
          zccrIsInsolventEnterprise: '否',
          zccrIsRelatedToMainBusiness: '否',
          zccrIsMajorShareholderLessHalfBoard: '否',
          zccrIsNetAssetLessRegisteredCapital: '否',
          zccrWithdrawalYear: 'xxxx',
          zccrStatus: '关注',
          zccrAnalysisSuggestion: '整改',
          zccrRectificationItems: '管理优化',
          zccrRectificationSuggestion: '',
          zccrIsRetained: '是',
          zccrIsReductionCompleted: '否'
        }
      ]
    },

    handleDetail (row) {
      console.log('查看详情:', row)
      this.$message.info('详情功能待实现')
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },

    // 计划审核相关方法
    handlePlanReview () {
      this.planReviewData = {}
      this.planReviewDialogVisible = true
    },

    // 处理计划审核确认
    handlePlanReviewConfirm (formData) {
      console.log('计划审核数据:', formData)
      this.$message.success('计划审核提交成功')
      // 这里可以调用API提交审核数据
      // this.submitPlanReview(formData)
    },

    // 查看计划
    handleViewPlan () {
      this.$message.info('查看计划功能待实现')
      // 这里可以跳转到计划详情页面或打开计划查看弹窗
    },

    // 选择要回退的计划
    handleSelectPlan (selectedPlans) {
      console.log('选择的回退计划:', selectedPlans)
      this.$message.success(`已选择 ${selectedPlans.length} 个回退计划`)
      // 这里可以处理选择的计划数据，比如保存到某个变量或发送到后端
    },

    // 处理计划建议确认
    handlePlanSuggestionConfirm (data) {
      console.log('计划建议确认:', data)
      this.$message.success(`对计划 ${data.planData.planName} 的建议已提交`)
      // 这里可以调用API提交建议数据
    },

    // 处理计划修正确认
    handlePlanCorrectionConfirm (data) {
      console.log('计划修正确认:', data)
      this.$message.success(`对计划 ${data.planData.planName} 的修正已提交`)
      // 这里可以调用API提交修正数据
    },

    // 处理计划详情
    handlePlanDetail (row) {
      console.log('计划详情:', row)
      this.$message.info(`查看计划 ${row.planName} 的详情`)
      // 这里可以打开详情弹窗或跳转到详情页面
    },

    // 建议功能
    handleSuggestion (row) {
      console.log('建议操作，行数据:', row)
      this.suggestionData = { ...row }
      this.suggestionDialogVisible = true
    },

    // 修正功能
    handleCorrection (row) {
      console.log('修正操作，行数据:', row)
      this.correctionData = { ...row }
      this.correctionDialogVisible = true
    },

    // 处理建议确认
    handleSuggestionConfirm (formData) {
      console.log('建议数据:', formData)
      console.log('当前资产数据:', this.suggestionData)
      this.$message.success('建议提交成功')
      // 这里可以调用API提交建议数据
      // this.submitSuggestion(formData, this.suggestionData)
    },

    // 处理修正确认
    handleCorrectionConfirm (formData) {
      console.log('修正数据:', formData)
      console.log('当前资产数据:', this.correctionData)
      this.$message.success('修正提交成功')
      // 这里可以调用API提交修正数据
      // this.submitCorrection(formData, this.correctionData)
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.status-block-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-block {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  text-align: center;
  min-width: 50px;
}

.status-danger {
  background-color: #f56c6c;
  border: 1px solid #f56c6c;
}

.status-success {
  background-color: #67c23a;
  border: 1px solid #67c23a;
}

.status-warning {
  background-color: #e6a23c;
  border: 1px solid #e6a23c;
}

.status-info {
  background-color: #909399;
  border: 1px solid #909399;
}
</style>
