<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="handlePlanAnalysis">参股企业分析</el-button>
        <el-button type="primary">推送成员单位</el-button>
        <el-button type="primary" @click="handlePlanReview">计划审核</el-button>
      </div>
      <el-button type="primary" icon="el-icon-search">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zccgId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zccgSecondLevelUnit" label="二级成员单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgInvestmentEntity" label="投资主体" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgHoldingEnterpriseName" label="参股企业名称" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgFirstTimeForGroup" label="我方是否为第一大股东（集团合计）" width="180" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgHasActualController" label="是否有实际控制人" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgDividendOverYears" label="是否满5年未分红" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgIsLongTermLoss" label="是否长期亏损" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgIsLowEfficiencyAsset" label="是否属于低效无效资产" width="160" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgIsNonContinuingOperation" label="是否非持续经营" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgTimelyDisclosureMajorMatters" label="是否能及时掌握财务数据和经营情况" width="200" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgLiquidationYear" label="清退年份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgStatus" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.zccgStatus)">
              {{ scope.row.zccgStatus || '未设置' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="zccgSecondLevelUnitSuggestion" label="分析建议" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgComprehensiveReasons" label="整改建议" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgIsProtected" label="是否保留" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgIsCompliantExit" label="是否完成清退" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" min-width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleSuggestion(scope.row)">建议</el-button>
            <el-button type="text" size="small" @click="handleCorrection(scope.row)">修正</el-button>
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 计划审核弹窗 -->
    <PlanReviewDialog :visible.sync="planReviewDialogVisible" :initial-data="planReviewData" @confirm="handlePlanReviewConfirm" @view-plan="handleViewPlan" @select-plan="handleSelectPlan" @suggestion-confirm="handlePlanSuggestionConfirm" @correction-confirm="handlePlanCorrectionConfirm" @detail="handlePlanDetail" />

    <!-- 建议弹窗 -->
    <SuggestionDialog :visible.sync="suggestionDialogVisible" :initial-data="suggestionData" @confirm="handleSuggestionConfirm" />

    <!-- 修正弹窗 -->
    <CorrectDialog :visible.sync="correctionDialogVisible" :initial-data="correctionData" @confirm="handleCorrectionConfirm" />
  </div>
</template>

<script>
import { getCompanyGoverList } from '@/api/digitalAssetSystem/treatment/analysis.js'
import PlanReviewDialog from './components/planReviewDialog.vue'
import SuggestionDialog from './components/suggestionDialog.vue'
import CorrectDialog from './components/correctDialog.vue'

export default {
  components: {
    PlanReviewDialog,
    SuggestionDialog,
    CorrectDialog
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 计划审核弹窗相关
      planReviewDialogVisible: false,
      planReviewData: {},
      // 建议弹窗相关
      suggestionDialogVisible: false,
      suggestionData: {},
      // 修正弹窗相关
      correctionDialogVisible: false,
      correctionData: {}
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getCompanyGoverList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          // 如果接口暂时没有数据，使用模拟数据展示表格效果
          this.tableData = this.getMockData()
          this.total = this.tableData.length
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        // 接口调用失败时使用模拟数据
        this.tableData = this.getMockData()
        this.total = this.tableData.length
        console.warn('接口调用失败，使用模拟数据')
      })
    },

    // 模拟数据方法
    getMockData () {
      return [
        {
          zccgId: '1',
          zccgSecondLevelUnit: 'xxx',
          zccgInvestmentEntity: 'xxx',
          zccgHoldingEnterpriseName: 'xxx',
          zccgFirstTimeForGroup: '是',
          zccgHasActualController: '否',
          zccgDividendOverYears: '是',
          zccgIsLongTermLoss: '否',
          zccgIsLowEfficiencyAsset: '否',
          zccgIsNonContinuingOperation: '否',
          zccgTimelyDisclosureMajorMatters: '是',
          zccgLiquidationYear: '2025',
          zccgStatus: '清退',
          zccgSecondLevelUnitSuggestion: 'xxx',
          zccgComprehensiveReasons: 'xxx',
          zccgIsProtected: '否',
          zccgIsCompliantExit: '否'
        },
        {
          zccgId: '2',
          zccgSecondLevelUnit: '',
          zccgInvestmentEntity: '',
          zccgHoldingEnterpriseName: '',
          zccgFirstTimeForGroup: '否',
          zccgHasActualController: '是',
          zccgDividendOverYears: '否',
          zccgIsLongTermLoss: '是',
          zccgIsLowEfficiencyAsset: '否',
          zccgIsNonContinuingOperation: '否',
          zccgTimelyDisclosureMajorMatters: '否',
          zccgLiquidationYear: '',
          zccgStatus: '关注',
          zccgSecondLevelUnitSuggestion: 'xxx',
          zccgComprehensiveReasons: '',
          zccgIsProtected: '是',
          zccgIsCompliantExit: '否'
        },
        {
          zccgId: '3',
          zccgSecondLevelUnit: '',
          zccgInvestmentEntity: '',
          zccgHoldingEnterpriseName: '',
          zccgFirstTimeForGroup: '是',
          zccgHasActualController: '否',
          zccgDividendOverYears: '否',
          zccgIsLongTermLoss: '否',
          zccgIsLowEfficiencyAsset: '是',
          zccgIsNonContinuingOperation: '否',
          zccgTimelyDisclosureMajorMatters: '是',
          zccgLiquidationYear: '',
          zccgStatus: '保留',
          zccgSecondLevelUnitSuggestion: '',
          zccgComprehensiveReasons: 'xxx',
          zccgIsProtected: '是',
          zccgIsCompliantExit: '否'
        },
        {
          zccgId: '4',
          zccgSecondLevelUnit: '',
          zccgInvestmentEntity: '',
          zccgHoldingEnterpriseName: '',
          zccgFirstTimeForGroup: '否',
          zccgHasActualController: '是',
          zccgDividendOverYears: '是',
          zccgIsLongTermLoss: '是',
          zccgIsLowEfficiencyAsset: '是',
          zccgIsNonContinuingOperation: '是',
          zccgTimelyDisclosureMajorMatters: '否',
          zccgLiquidationYear: '2024',
          zccgStatus: '清退',
          zccgSecondLevelUnitSuggestion: '',
          zccgComprehensiveReasons: '',
          zccgIsProtected: '否',
          zccgIsCompliantExit: '是'
        }
      ]
    },

    handleDetail (row) {
      console.log('查看详情:', row)
      this.$message.info('详情功能待实现')
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },

    handlePlanAnalysis () {
      this.fetchData()
    },

    // 计划审核相关方法
    handlePlanReview () {
      this.planReviewData = {}
      this.planReviewDialogVisible = true
    },

    // 处理计划审核确认
    handlePlanReviewConfirm (formData) {
      console.log('计划审核数据:', formData)
      this.$message.success('计划审核提交成功')
      // 这里可以调用API提交审核数据
      // this.submitPlanReview(formData)
    },

    // 查看计划
    handleViewPlan () {
      this.$message.info('查看计划功能待实现')
      // 这里可以跳转到计划详情页面或打开计划查看弹窗
    },

    // 选择要回退的计划
    handleSelectPlan (selectedPlans) {
      console.log('选择的回退计划:', selectedPlans)
      this.$message.success(`已选择 ${selectedPlans.length} 个回退计划`)
      // 这里可以处理选择的计划数据，比如保存到某个变量或发送到后端
    },

    // 处理计划建议确认
    handlePlanSuggestionConfirm (data) {
      console.log('计划建议确认:', data)
      this.$message.success(`对计划 ${data.planData.planName} 的建议已提交`)
      // 这里可以调用API提交建议数据
    },

    // 处理计划修正确认
    handlePlanCorrectionConfirm (data) {
      console.log('计划修正确认:', data)
      this.$message.success(`对计划 ${data.planData.planName} 的修正已提交`)
      // 这里可以调用API提交修正数据
    },

    // 处理计划详情
    handlePlanDetail (row) {
      console.log('计划详情:', row)
      this.$message.info(`查看计划 ${row.planName} 的详情`)
      // 这里可以打开详情弹窗或跳转到详情页面
    },

    // 建议功能
    handleSuggestion (row) {
      console.log('建议操作，行数据:', row)
      this.suggestionData = { ...row }
      this.suggestionDialogVisible = true
    },

    // 修正功能
    handleCorrection (row) {
      console.log('修正操作，行数据:', row)
      this.correctionData = { ...row }
      this.correctionDialogVisible = true
    },

    // 处理建议确认
    handleSuggestionConfirm (formData) {
      console.log('建议数据:', formData)
      console.log('当前资产数据:', this.suggestionData)
      this.$message.success('建议提交成功')
      // 这里可以调用API提交建议数据
      // this.submitSuggestion(formData, this.suggestionData)
    },

    // 处理修正确认
    handleCorrectionConfirm (formData) {
      console.log('修正数据:', formData)
      console.log('当前资产数据:', this.correctionData)
      this.$message.success('修正提交成功')
      // 这里可以调用API提交修正数据
      // this.submitCorrection(formData, this.correctionData)
    },

    // 获取状态标签类型
    getStatusType (status) {
      const statusMap = {
        '绿灯': 'success',
        '黄灯': 'warning',
        '红灯': 'danger'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.status-block-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-block {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  text-align: center;
  min-width: 50px;
}

.status-danger {
  background-color: #f56c6c;
  border: 1px solid #f56c6c;
}

.status-success {
  background-color: #67c23a;
  border: 1px solid #67c23a;
}

.status-warning {
  background-color: #e6a23c;
  border: 1px solid #e6a23c;
}

.status-info {
  background-color: #909399;
  border: 1px solid #909399;
}
</style>
